<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        .test-step {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .test-step h4 {
            margin: 0 0 8px 0;
            color: #007aff;
        }
        .test-step p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        .expected-result {
            background: #e8f5e8;
            border-left: 4px solid #34c759;
            padding: 10px;
            margin-top: 10px;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ff9500;
            padding: 10px;
            margin-top: 10px;
        }
        .link-button {
            display: inline-block;
            background: #007aff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            margin: 10px 10px 10px 0;
        }
        .link-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 导航栏主题切换按钮修复测试</h1>
        
        <div class="test-section">
            <h2>问题描述</h2>
            <p>之前导航栏中出现了两个主题切换按钮，这是因为代码中使用了Tailwind CSS的响应式类名（如 <code>hidden md:block</code>），但项目中并未安装Tailwind CSS，导致这些类名不生效。</p>
        </div>

        <div class="test-section">
            <h2>修复方案</h2>
            <p>将Tailwind CSS类名替换为自定义CSS类名，并在 <code>global.css</code> 中添加相应的媒体查询规则来控制桌面端和移动端的显示。</p>
        </div>

        <div class="test-section">
            <h2>测试步骤</h2>
            
            <div class="test-step">
                <h4>1. 桌面端测试（屏幕宽度 > 768px）</h4>
                <p>在桌面浏览器中访问网站，检查导航栏右侧是否只显示一个主题切换按钮。</p>
                <div class="expected-result">
                    <strong>预期结果：</strong> 只显示一个主题切换按钮，位于导航栏右侧
                </div>
            </div>

            <div class="test-step">
                <h4>2. 移动端测试（屏幕宽度 ≤ 768px）</h4>
                <p>将浏览器窗口缩小到移动端尺寸，或使用开发者工具切换到移动设备视图。</p>
                <div class="expected-result">
                    <strong>预期结果：</strong> 只显示一个小尺寸的主题切换按钮，位于汉堡菜单按钮左侧
                </div>
            </div>

            <div class="test-step">
                <h4>3. 响应式切换测试</h4>
                <p>在桌面端和移动端之间切换，观察主题切换按钮的显示变化。</p>
                <div class="expected-result">
                    <strong>预期结果：</strong> 在不同屏幕尺寸下，始终只显示一个主题切换按钮
                </div>
            </div>

            <div class="test-step">
                <h4>4. 功能测试</h4>
                <p>点击主题切换按钮，验证主题切换功能是否正常工作。</p>
                <div class="expected-result">
                    <strong>预期结果：</strong> 主题能够正常在浅色和暗黑模式之间切换
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>快速测试链接</h2>
            <a href="http://localhost:5174/" class="link-button" target="_blank">打开主页</a>
            <a href="http://localhost:5174/team" class="link-button" target="_blank">团队页面</a>
            <a href="http://localhost:5174/theme-test" class="link-button" target="_blank">主题测试页面</a>
        </div>

        <div class="test-section">
            <h2>技术细节</h2>
            <div class="warning">
                <strong>修复内容：</strong>
                <ul>
                    <li>移除了无效的Tailwind CSS类名（<code>hidden md:block</code>, <code>md:hidden</code>）</li>
                    <li>添加了自定义CSS类名（<code>desktop-menu</code>, <code>desktop-actions</code>, <code>mobile-actions</code>）</li>
                    <li>在 <code>global.css</code> 中添加了媒体查询规则来控制响应式显示</li>
                    <li>确保在不同屏幕尺寸下只显示一个主题切换按钮</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
