# 团队成员页面键盘交互修复说明

## 问题描述
在团队成员页面中，当用户在成员卡片区域按下"Esc"键时，成员卡片的父级容器会意外显示一个蓝色的矩形边框（focus outline）。

## 问题根本原因
1. **Ant Design Modal的焦点管理**：Modal关闭时会自动将焦点返回到触发元素
2. **Card组件的可聚焦特性**：设置了onClick的Card组件会自动获得tabIndex，变成可聚焦元素
3. **浏览器默认行为**：元素获得焦点时显示默认的focus outline

## 修复方案

### 1. 自定义Focus样式
- 添加了自定义CSS类 `.team-member-card` 
- 使用 `:focus:not(:focus-visible)` 选择器隐藏鼠标点击后的focus outline
- 保留 `:focus-visible` 样式确保键盘导航的可访问性

### 2. 焦点管理优化
- 在Modal关闭时主动移除触发元素的焦点
- 添加全局键盘事件监听器处理Esc键
- 记录触发Modal的元素，精确控制焦点移除

### 3. 键盘交互增强
- 为可点击的卡片添加键盘支持（Enter和Space键）
- 设置合适的tabIndex值
- 保持完整的键盘导航功能

## 修复内容

### CSS样式修复
```css
.team-member-card.ant-card:focus:not(:focus-visible) {
  box-shadow: var(--shadow-md) !important;
}

.team-member-card.ant-card:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 2px var(--color-primary) !important;
}
```

### JavaScript逻辑修复
1. **Modal关闭处理**：延迟移除焦点，避免focus outline显示
2. **全局键盘监听**：处理Esc键按下时的焦点清理
3. **事件处理增强**：支持键盘激活卡片

## 测试验证

### 测试步骤
1. 访问团队成员页面
2. 点击Mentor或Collaborator卡片打开Modal
3. 按Esc键关闭Modal
4. 验证不会显示蓝色边框
5. 使用Tab键测试键盘导航
6. 使用Enter/Space键测试键盘激活

### 预期结果
- ✅ 按Esc键关闭Modal后不显示focus outline
- ✅ Tab键导航正常工作
- ✅ Enter/Space键可以激活卡片
- ✅ 键盘导航时显示适当的focus样式
- ✅ 鼠标交互不受影响

## 可访问性保证
- 保留了键盘导航功能
- 使用 `:focus-visible` 确保键盘用户能看到焦点指示
- 支持Enter和Space键激活
- 维持了正确的tabIndex设置

## 浏览器兼容性
- 现代浏览器完全支持
- `:focus-visible` 在较老浏览器中会降级为 `:focus`
- 使用了标准的DOM API，兼容性良好
