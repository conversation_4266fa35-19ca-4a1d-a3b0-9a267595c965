<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色模式默认主题测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-step {
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>深色模式默认主题测试指南</h1>
        <p>使用此页面测试网站的深色模式默认设置是否正确工作。</p>

        <div class="test-section">
            <div class="test-title">🧪 测试1：清除本地存储测试</div>
            <div class="test-step">
                <strong>目的：</strong>验证在没有用户偏好设置时，网站默认使用深色主题
            </div>
            <div class="test-step">
                <strong>步骤：</strong>
                <ol>
                    <li>打开浏览器开发者工具 (F12)</li>
                    <li>在控制台中执行：<span class="code">localStorage.removeItem('research-group-theme')</span></li>
                    <li>刷新页面：<span class="code">location.reload()</span></li>
                    <li>观察网站主题</li>
                </ol>
            </div>
            <div class="test-step">
                <strong class="success">预期结果：</strong>网站显示深色主题
            </div>
            <button onclick="clearStorageAndReload()">一键执行测试1</button>
        </div>

        <div class="test-section">
            <div class="test-title">🌙 测试2：系统偏好测试</div>
            <div class="test-step">
                <strong>目的：</strong>验证系统偏好设置的影响
            </div>
            <div class="test-step">
                <strong>步骤：</strong>
                <ol>
                    <li>在操作系统中设置为浅色模式</li>
                    <li>清除本地存储并刷新页面</li>
                    <li>观察网站主题</li>
                    <li>在操作系统中设置为深色模式</li>
                    <li>再次清除本地存储并刷新页面</li>
                </ol>
            </div>
            <div class="test-step">
                <strong class="success">预期结果：</strong>
                <ul>
                    <li>系统浅色模式 → 网站显示浅色主题</li>
                    <li>系统深色模式 → 网站显示深色主题</li>
                    <li>系统无偏好 → 网站显示深色主题</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔄 测试3：主题切换功能测试</div>
            <div class="test-step">
                <strong>目的：</strong>验证手动主题切换功能正常工作
            </div>
            <div class="test-step">
                <strong>步骤：</strong>
                <ol>
                    <li>点击网站的主题切换按钮</li>
                    <li>观察主题是否正确切换</li>
                    <li>刷新页面，观察主题是否保持</li>
                    <li>再次切换主题，验证双向切换</li>
                </ol>
            </div>
            <div class="test-step">
                <strong class="success">预期结果：</strong>主题切换正常，设置持久保存
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📱 测试4：页面兼容性测试</div>
            <div class="test-step">
                <strong>目的：</strong>验证所有页面在深色模式下正确显示
            </div>
            <div class="test-step">
                <strong>测试页面：</strong>
                <ul>
                    <li>首页 (Home)</li>
                    <li>团队成员 (Team)</li>
                    <li>研究方向 (Research)</li>
                    <li>项目展示 (Projects)</li>
                    <li>论文发表 (Publications)</li>
                    <li>新闻动态 (News)</li>
                    <li>联系我们 (Contact)</li>
                </ul>
            </div>
            <div class="test-step">
                <strong class="success">预期结果：</strong>所有页面在深色模式下显示正常，无样式问题
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 测试5：开发者工具检查</div>
            <div class="test-step">
                <strong>检查项目：</strong>
                <ol>
                    <li>HTML根元素应有 <span class="code">data-theme="dark"</span> 属性</li>
                    <li>CSS变量值应为深色主题值</li>
                    <li>本地存储中应保存用户的主题选择</li>
                </ol>
            </div>
            <button onclick="checkThemeStatus()">检查当前主题状态</button>
            <div id="themeStatus" style="margin-top: 10px;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 测试结果记录</div>
            <div class="test-step">
                <strong class="info">请在完成测试后记录结果：</strong>
                <ul>
                    <li>✅ 测试1 - 默认深色主题：<input type="checkbox" id="test1"></li>
                    <li>✅ 测试2 - 系统偏好响应：<input type="checkbox" id="test2"></li>
                    <li>✅ 测试3 - 主题切换功能：<input type="checkbox" id="test3"></li>
                    <li>✅ 测试4 - 页面兼容性：<input type="checkbox" id="test4"></li>
                    <li>✅ 测试5 - 开发者检查：<input type="checkbox" id="test5"></li>
                </ul>
            </div>
            <button onclick="generateReport()">生成测试报告</button>
            <div id="testReport" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        function clearStorageAndReload() {
            if (confirm('这将清除主题设置并刷新页面，确定继续？')) {
                localStorage.removeItem('research-group-theme');
                location.reload();
            }
        }

        function checkThemeStatus() {
            const statusDiv = document.getElementById('themeStatus');
            const dataTheme = document.documentElement.getAttribute('data-theme');
            const storedTheme = localStorage.getItem('research-group-theme');
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            statusDiv.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <strong>当前主题状态：</strong><br>
                    • HTML data-theme: <span class="code">${dataTheme || '未设置'}</span><br>
                    • 本地存储主题: <span class="code">${storedTheme || '无'}</span><br>
                    • 系统偏好深色: <span class="code">${systemPrefersDark}</span><br>
                    • 当前时间: <span class="code">${new Date().toLocaleString()}</span>
                </div>
            `;
        }

        function generateReport() {
            const tests = ['test1', 'test2', 'test3', 'test4', 'test5'];
            const results = tests.map(id => ({
                id,
                passed: document.getElementById(id).checked
            }));
            
            const passedCount = results.filter(r => r.passed).length;
            const reportDiv = document.getElementById('testReport');
            
            reportDiv.innerHTML = `
                <div style="background: ${passedCount === 5 ? '#d4edda' : '#fff3cd'}; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <strong>测试报告 (${new Date().toLocaleString()})</strong><br>
                    通过测试: ${passedCount}/5<br>
                    ${passedCount === 5 ? 
                        '<span class="success">🎉 所有测试通过！深色模式默认设置工作正常。</span>' : 
                        '<span class="warning">⚠️ 部分测试未通过，请检查相关功能。</span>'
                    }
                </div>
            `;
        }

        // 页面加载时自动检查主题状态
        window.addEventListener('load', checkThemeStatus);
    </script>
</body>
</html>
