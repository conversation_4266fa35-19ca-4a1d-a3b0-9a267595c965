import React, { useState } from 'react';
import { Layout as AntLayout, <PERSON>u, Button, Dropdown, Drawer } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  TeamOutlined,
  ExperimentOutlined,
  ProjectOutlined,
  BookOutlined,
  FileTextOutlined,
  ContactsOutlined,
  MenuOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import CenteredContainer from '../CenteredContainer';
import ThemeToggle from '../ThemeToggle';
import { useTheme } from '../../contexts/theme';
import type { MenuProps } from 'antd';

const { Header, Content, Footer } = AntLayout;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const { theme } = useTheme();

  // 检查是否为首页，首页需要特殊的全屏布局处理
  const isHomePage = location.pathname === '/';

  const menuItems: MenuProps['items'] = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/team',
      icon: <TeamOutlined />,
      label: <Link to="/team">团队成员</Link>,
    },
    {
      key: '/research',
      icon: <ExperimentOutlined />,
      label: <Link to="/research">研究方向</Link>,
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: <Link to="/projects">科研项目</Link>,
    },
    {
      key: '/publications',
      icon: <BookOutlined />,
      label: <Link to="/publications">学术成果</Link>,
    },
    {
      key: '/news',
      icon: <FileTextOutlined />,
      label: <Link to="/news">新闻动态</Link>,
    },
    {
      key: '/contact',
      icon: <ContactsOutlined />,
      label: <Link to="/contact">联系我们</Link>,
    },
  ];

  const mobileMenuItems: MenuProps['items'] = [
    {
      key: 'menu',
      icon: <MenuOutlined />,
      children: menuItems,
    },
  ];

  return (
    <AntLayout style={{ minHeight: '100vh', background: 'var(--bg-primary)' }}>
      {/* 苹果风格导航栏 */}
      <Header
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          background: `rgba(${theme.mode === 'dark' ? '28, 28, 30' : '255, 255, 255'}, ${isHomePage ? '0.8' : '0.95'})`,
          backdropFilter: 'blur(20px)',
          borderBottom: `1px solid var(--border-primary)`,
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: isHomePage ? 100 : 1000, // 首页时降低z-index，让3D背景更突出
          height: '72px',
          padding: '0 32px',
          boxShadow: isHomePage ? 'none' : 'var(--shadow-md)',
          transition: 'all 0.3s ease'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {/* Logo */}
          <Link to="/" style={{ textDecoration: 'none' }}>
            <div style={{
              color: 'var(--text-primary)',
              fontSize: '1.5rem',
              fontWeight: '700',
              marginRight: '48px',
              letterSpacing: '-0.02em',
              transition: 'color 0.3s ease'
            }}>
              GeoCUES Lab
            </div>
          </Link>

          {/* 桌面端菜单 */}
          <div className="desktop-menu">
            <Menu
              mode="horizontal"
              selectedKeys={[location.pathname]}
              items={menuItems}
              style={{
                borderBottom: 'none',
                background: 'transparent',
                fontSize: '16px',
                fontWeight: '500'
              }}
              theme={theme.mode === 'dark' ? 'dark' : 'light'}
            />
          </div>
        </div>

        {/* 桌面端右侧操作区域 */}
        <div
          className="desktop-actions"
          style={{
            alignItems: 'center',
            gap: '12px'
          }}
        >
          {/* 主题切换按钮 */}
          <ThemeToggle />
        </div>

        {/* 移动端菜单按钮和主题切换 */}
        <div
          className="mobile-actions"
          style={{
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <ThemeToggle size="small" />
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            style={{
              color: 'var(--text-primary)',
              fontSize: '18px',
              width: '40px',
              height: '40px',
              borderRadius: '12px',
              transition: 'color 0.3s ease'
            }}
          />
        </div>
      </Header>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontSize: '18px',
            fontWeight: '600',
            color: 'var(--text-primary)'
          }}>
            课题组网站
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setMobileMenuVisible(false)}
              style={{ color: 'var(--text-secondary)' }}
            />
          </div>
        }
        placement="right"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        width={280}
        closable={false}
        styles={{
          body: {
            padding: 0,
            background: 'var(--bg-secondary)'
          },
          header: {
            borderBottom: `1px solid var(--border-primary)`,
            background: 'var(--bg-secondary)'
          }
        }}
      >
        <Menu
          mode="vertical"
          selectedKeys={[location.pathname]}
          items={menuItems.map(item => ({
            ...item,
            onClick: () => setMobileMenuVisible(false)
          }))}
          style={{
            border: 'none',
            fontSize: '16px',
            background: 'var(--bg-secondary)'
          }}
          theme={theme.mode === 'dark' ? 'dark' : 'light'}
        />
      </Drawer>

      <Content style={{
        // 首页使用全屏布局，其他页面使用常规布局
        padding: isHomePage ? '0' : '48px 0',
        marginTop: isHomePage ? '0' : '72px',
        minHeight: isHomePage ? '100vh' : 'calc(100vh - 72px - 120px)',
        width: isHomePage ? '100vw' : '100%',
        background: 'var(--bg-primary)',
        transition: 'background-color 0.3s ease',
        position: isHomePage ? 'relative' : 'static',
        overflow: isHomePage ? 'hidden' : 'visible'
      }}>
        {isHomePage ? (
          // 首页直接渲染children，不使用CenteredContainer
          children
        ) : (
          // 其他页面使用CenteredContainer
          <CenteredContainer>
            {children}
          </CenteredContainer>
        )}
      </Content>

      {/* 苹果风格页脚 - 首页时隐藏 */}
      {!isHomePage && (
        <Footer style={{
          textAlign: 'center',
          background: 'var(--bg-tertiary)',
          borderTop: `1px solid var(--border-primary)`,
          padding: '48px 32px',
          transition: 'all 0.3s ease'
        }}>
          <div style={{
            marginBottom: '16px',
            fontSize: '18px',
            fontWeight: '600',
            color: 'var(--text-primary)',
            transition: 'color 0.3s ease'
          }}>
            GeoCUES Lab
          </div>
          <div style={{
            fontSize: '14px',
            color: 'var(--text-secondary)',
            lineHeight: '1.6',
            transition: 'color 0.3s ease'
          }}>
            基于 React + Strapi + Ant Design 构建<br />
            © 2024 版权所有
          </div>
        </Footer>
      )}
    </AntLayout>
  );
};

export default Layout; 