import React from 'react';
import { Row, Col, Card, Typography, Space, Button, Badge } from 'antd';
import {
  TeamOutlined,
  ExperimentOutlined,
  ProjectOutlined,
  ArrowRightOutlined,
  StarOutlined,
  RocketOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';

import { useScrollSnap } from '../../hooks/useScrollSnap';
import CountryOutlineBackground from '../../components/CountryOutlineBackground';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  const { registerSection, scrollToSection, currentSection } = useScrollSnap();

  // 研究方向数据
  const researchAreas = [
    {
      title: '地理空间分析',
      description: '空间数据挖掘、地理信息系统、空间统计分析',
      color: '#007aff'
    },
    {
      title: '城市计算',
      description: '智慧城市、城市大数据、交通流分析',
      color: '#34c759'
    },
    {
      title: '经济地理建模',
      description: '区域经济分析、产业空间布局、经济网络',
      color: '#ff9500'
    },
    {
      title: '社会感知计算',
      description: '社交网络分析、人群行为建模、社会经济指标',
      color: '#ff3b30'
    }
  ];

  return (
    <div className="home-container">
      {/* Section 1 - Hero/Landing */}
      <section
        ref={(el) => registerSection(el as HTMLDivElement, 0)}
        className="home-section"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%)',
          overflow: 'hidden'
        }}
      >
        <CountryOutlineBackground />

        <div style={{
          textAlign: 'center',
          zIndex: 10,
          maxWidth: '900px',
          padding: '0 32px',
          color: 'var(--text-primary)'
        }}>
          <div style={{
            fontSize: '1.2rem',
            fontWeight: '500',
            marginBottom: '24px',
            opacity: 0.8,
            letterSpacing: '0.1em',
            textTransform: 'uppercase'
          }}>
            欢迎来到
          </div>

          <Title
            level={1}
            style={{
              color: 'var(--text-primary)',
              marginBottom: '16px',
              fontSize: '4.5rem',
              fontWeight: '800',
              letterSpacing: '-0.03em',
              lineHeight: '1.1',
              background: 'linear-gradient(135deg, var(--color-primary), var(--color-secondary))',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              position: 'relative',
              textShadow: `
                0 0 10px var(--color-primary)40,
                0 0 20px var(--color-primary)30,
                0 0 40px var(--color-primary)20,
                0 0 80px var(--color-primary)10
              `,
              animation: 'glow-pulse 3s ease-in-out infinite alternate'
            }}
            className="geocues-title"
          >
            GeoCUES Lab
          </Title>

          <Title
            level={2}
            style={{
              color: 'var(--text-secondary)',
              marginBottom: '32px',
              fontSize: '2rem',
              fontWeight: '400',
              letterSpacing: '-0.01em',
              lineHeight: '1.3'
            }}
          >
            Geographic Computation for Urban Economy & Society
          </Title>

          <Paragraph style={{
            fontSize: '1.25rem',
            lineHeight: '1.7',
            color: 'var(--text-secondary)',
            maxWidth: '700px',
            margin: '0 auto 48px',
            opacity: 0.9
          }}>
            致力于地理计算、城市经济与社会研究的前沿实验室，
            通过先进的计算方法和数据分析技术，探索城市发展的空间规律与社会经济机制
          </Paragraph>

          <Space size="large">
            <Button
              type="primary"
              size="large"
              onClick={() => scrollToSection(1)}
              style={{
                background: 'var(--color-primary)',
                borderColor: 'var(--color-primary)',
                borderRadius: '12px',
                height: '56px',
                fontSize: '18px',
                fontWeight: '600',
                padding: '0 32px',
                boxShadow: 'var(--shadow-lg)'
              }}
              icon={<ArrowRightOutlined />}
            >
              了解更多
            </Button>

            <Button
              size="large"
              onClick={() => scrollToSection(3)}
              style={{
                background: 'transparent',
                borderColor: 'var(--color-primary)',
                color: 'var(--color-primary)',
                borderRadius: '12px',
                height: '56px',
                fontSize: '18px',
                fontWeight: '600',
                padding: '0 32px'
              }}
            >
              联系我们
            </Button>
          </Space>
        </div>
      </section>

      {/* Section 2 - Overview Cards */}
      <section
        ref={(el) => registerSection(el as HTMLDivElement, 1)}
        className="home-section"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'var(--bg-secondary)',
          padding: '0 32px',
          boxSizing: 'border-box'
        }}
      >
        <div style={{ maxWidth: '1200px', width: '100%' }}>
          <div style={{ textAlign: 'center', marginBottom: '80px' }}>
            <Title
              level={2}
              style={{
                color: 'var(--text-primary)',
                fontSize: '3rem',
                fontWeight: '700',
                marginBottom: '24px',
                letterSpacing: '-0.02em'
              }}
            >
              实验室概览
            </Title>
            <Paragraph style={{
              fontSize: '1.25rem',
              color: 'var(--text-secondary)',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              了解我们的团队规模、研究领域和学术成果
            </Paragraph>
          </div>

          <Row gutter={[48, 48]} justify="center">
            <Col xs={24} sm={12} lg={6}>
              <Card
                style={{
                  borderRadius: '24px',
                  border: 'none',
                  background: 'var(--bg-card)',
                  boxShadow: 'var(--shadow-lg)',
                  transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  cursor: 'pointer',
                  height: '280px'
                }}
                hoverable
                onClick={() => scrollToSection(2)}
              >
                <div style={{ textAlign: 'center', padding: '24px' }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(135deg, #007aff20, #007aff10)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 24px',
                    fontSize: '32px',
                    color: '#007aff'
                  }}>
                    <TeamOutlined />
                  </div>
                  <Title level={3} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                    团队成员
                  </Title>
                  <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                    多元化的研究团队，包括导师、博士生、硕士生等
                  </Paragraph>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={12} lg={6}>
              <Card
                style={{
                  borderRadius: '24px',
                  border: 'none',
                  background: 'var(--bg-card)',
                  boxShadow: 'var(--shadow-lg)',
                  transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  cursor: 'pointer',
                  height: '280px'
                }}
                hoverable
                onClick={() => scrollToSection(2)}
              >
                <div style={{ textAlign: 'center', padding: '24px' }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(135deg, #34c75920, #34c75910)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 24px',
                    fontSize: '32px',
                    color: '#34c759'
                  }}>
                    <ExperimentOutlined />
                  </div>
                  <Title level={3} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                    研究领域
                  </Title>
                  <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                    地理计算、城市经济、社会感知等前沿研究方向
                  </Paragraph>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={12} lg={6}>
              <Card
                style={{
                  borderRadius: '24px',
                  border: 'none',
                  background: 'var(--bg-card)',
                  boxShadow: 'var(--shadow-lg)',
                  transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  cursor: 'pointer',
                  height: '280px'
                }}
                hoverable
              >
                <div style={{ textAlign: 'center', padding: '24px' }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(135deg, #ff950020, #ff950010)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 24px',
                    fontSize: '32px',
                    color: '#ff9500'
                  }}>
                    <ProjectOutlined />
                  </div>
                  <Title level={3} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                    当前项目
                  </Title>
                  <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                    国家级、省部级重点项目及产学研合作项目
                  </Paragraph>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* Section 3 - About & Research */}
      <section
        ref={(el) => registerSection(el as HTMLDivElement, 2)}
        className="home-section"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'var(--bg-primary)',
          padding: '0 32px',
          boxSizing: 'border-box'
        }}
      >
        <div style={{ maxWidth: '1200px', width: '100%' }}>
          <Row gutter={[64, 64]} align="middle">
            <Col xs={24} lg={12}>
              <div style={{ marginBottom: '32px' }}>
                <Badge
                  count={<StarOutlined style={{ color: 'var(--color-primary)' }} />}
                  style={{ backgroundColor: 'transparent' }}
                >
                  <Title level={2} style={{
                    margin: 0,
                    color: 'var(--text-primary)',
                    fontWeight: '700',
                    fontSize: '2.5rem',
                    marginBottom: '24px'
                  }}>
                    实验室简介
                  </Title>
                </Badge>
              </div>

              <Paragraph style={{
                fontSize: '1.125rem',
                lineHeight: '1.8',
                color: 'var(--text-primary)',
                marginBottom: '24px'
              }}>
                GeoCUES Lab 是一个专注于地理计算、城市经济与社会研究的前沿实验室。
                我们致力于运用先进的计算方法、大数据分析和人工智能技术，
                探索城市发展的空间规律与社会经济机制。
              </Paragraph>

              <Paragraph style={{
                fontSize: '1.125rem',
                lineHeight: '1.8',
                color: 'var(--text-primary)',
                marginBottom: '40px'
              }}>
                实验室汇聚了地理学、经济学、计算机科学等多学科背景的研究人员，
                形成了跨学科的创新研究团队，为解决城市发展中的复杂问题提供科学支撑。
              </Paragraph>

              <Link to="/team">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    background: 'var(--color-primary)',
                    borderColor: 'var(--color-primary)',
                    borderRadius: '16px',
                    height: '56px',
                    fontSize: '18px',
                    fontWeight: '600',
                    padding: '0 32px',
                    boxShadow: 'var(--shadow-lg)'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  了解团队成员
                </Button>
              </Link>
            </Col>

            <Col xs={24} lg={12}>
              <div style={{ marginBottom: '32px' }}>
                <Badge
                  count={<RocketOutlined style={{ color: 'var(--color-success)' }} />}
                  style={{ backgroundColor: 'transparent' }}
                >
                  <Title level={2} style={{
                    margin: 0,
                    color: 'var(--text-primary)',
                    fontWeight: '700',
                    fontSize: '2.5rem',
                    marginBottom: '24px'
                  }}>
                    研究方向
                  </Title>
                </Badge>
              </div>

              <Space direction="vertical" style={{ width: '100%' }} size={24}>
                {researchAreas.map((item, index) => (
                  <div key={index} style={{
                    padding: '24px',
                    borderRadius: '16px',
                    background: 'var(--bg-card)',
                    border: `2px solid ${item.color}20`,
                    boxShadow: 'var(--shadow-sm)',
                    transition: 'all 0.3s ease'
                  }}>
                    <div style={{
                      fontSize: '1.25rem',
                      fontWeight: '700',
                      color: item.color,
                      marginBottom: '12px'
                    }}>
                      {item.title}
                    </div>
                    <div style={{
                      fontSize: '1rem',
                      color: 'var(--text-secondary)',
                      lineHeight: '1.6'
                    }}>
                      {item.description}
                    </div>
                  </div>
                ))}
              </Space>

              <Link to="/research">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    background: 'var(--color-success)',
                    borderColor: 'var(--color-success)',
                    borderRadius: '16px',
                    height: '56px',
                    fontSize: '18px',
                    fontWeight: '600',
                    marginTop: '40px',
                    padding: '0 32px',
                    boxShadow: 'var(--shadow-lg)'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  探索研究领域
                </Button>
              </Link>
            </Col>
          </Row>
        </div>
      </section>

      {/* Section 4 - Contact */}
      <section
        ref={(el) => registerSection(el as HTMLDivElement, 3)}
        className="home-section"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'var(--bg-secondary)',
          padding: '0 32px',
          boxSizing: 'border-box'
        }}
      >
        <div style={{ maxWidth: '800px', width: '100%', textAlign: 'center' }}>
          <div style={{ marginBottom: '64px' }}>
            <Title
              level={2}
              style={{
                color: 'var(--text-primary)',
                fontSize: '3rem',
                fontWeight: '700',
                marginBottom: '24px',
                letterSpacing: '-0.02em'
              }}
            >
              联系我们
            </Title>
            <Paragraph style={{
              fontSize: '1.25rem',
              color: 'var(--text-secondary)',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              欢迎与我们交流合作，共同推进地理计算与城市研究的发展
            </Paragraph>
          </div>

          <Row gutter={[48, 48]} justify="center">
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: '80px',
                  height: '80px',
                  borderRadius: '20px',
                  background: 'linear-gradient(135deg, #007aff20, #007aff10)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 24px',
                  fontSize: '32px',
                  color: '#007aff'
                }}>
                  <EnvironmentOutlined />
                </div>
                <Title level={4} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                  实验室地址
                </Title>
                <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                  北京市海淀区学院路XX号<br />
                  XX大学XX楼XX室
                </Paragraph>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: '80px',
                  height: '80px',
                  borderRadius: '20px',
                  background: 'linear-gradient(135deg, #34c75920, #34c75910)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 24px',
                  fontSize: '32px',
                  color: '#34c759'
                }}>
                  <MailOutlined />
                </div>
                <Title level={4} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                  邮箱联系
                </Title>
                <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                  <EMAIL><br />
                  <EMAIL>
                </Paragraph>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: '80px',
                  height: '80px',
                  borderRadius: '20px',
                  background: 'linear-gradient(135deg, #ff950020, #ff950010)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 24px',
                  fontSize: '32px',
                  color: '#ff9500'
                }}>
                  <PhoneOutlined />
                </div>
                <Title level={4} style={{ color: 'var(--text-primary)', marginBottom: '12px' }}>
                  电话联系
                </Title>
                <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
                  +86 010-XXXX-XXXX<br />
                  +86 138-XXXX-XXXX
                </Paragraph>
              </div>
            </Col>
          </Row>

          <div style={{ marginTop: '64px' }}>
            <Space size="large">
              <Link to="/contact">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    background: 'var(--color-primary)',
                    borderColor: 'var(--color-primary)',
                    borderRadius: '16px',
                    height: '56px',
                    fontSize: '18px',
                    fontWeight: '600',
                    padding: '0 32px',
                    boxShadow: 'var(--shadow-lg)'
                  }}
                  icon={<ArrowRightOutlined />}
                >
                  详细联系方式
                </Button>
              </Link>

              <Button
                size="large"
                onClick={() => scrollToSection(0)}
                style={{
                  background: 'transparent',
                  borderColor: 'var(--color-primary)',
                  color: 'var(--color-primary)',
                  borderRadius: '16px',
                  height: '56px',
                  fontSize: '18px',
                  fontWeight: '600',
                  padding: '0 32px'
                }}
              >
                返回顶部
              </Button>
            </Space>
          </div>
        </div>
      </section>

      {/* Section导航指示器 */}
      <div className="section-navigator">
        {[0, 1, 2, 3].map((index) => (
          <div
            key={index}
            onClick={() => scrollToSection(index)}
            className={`section-dot ${currentSection === index ? 'active' : 'inactive'}`}
            title={`跳转到第${index + 1}部分`}
          />
        ))}
      </div>
    </div>
  );
};

export default Home; 