# 默认主题修改为深色模式

## 修改概述
已成功将网站的默认主题从浅色模式修改为深色模式。用户首次访问网站时将看到深色主题界面。

## 修改内容

### 1. 主题配置文件 (`themeConfig.ts`)
```typescript
// 修改前
export const defaultTheme = lightTheme;

// 修改后
export const defaultTheme = darkTheme;
```

### 2. 主题提供者 (`ThemeProvider.tsx`)

#### 2.1 系统主题获取函数
```typescript
// 修改默认返回值为深色主题
const getSystemTheme = (): ThemeMode => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'dark'; // 从 'light' 改为 'dark'
};
```

#### 2.2 主题初始化逻辑
```typescript
const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
  // 优先级：本地存储 > 系统偏好 > 默认主题（深色）
  const storedTheme = getStoredTheme();
  if (storedTheme) {
    return storedTheme;
  }
  
  // 检查系统偏好，如果没有则使用深色主题作为默认
  if (typeof window !== 'undefined' && window.matchMedia) {
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const systemPrefersLight = window.matchMedia('(prefers-color-scheme: light)').matches;
    
    // 只有在系统明确偏好浅色时才使用浅色，否则使用深色
    if (systemPrefersLight && !systemPrefersDark) {
      return 'light';
    }
  }
  
  // 默认使用深色主题
  return 'dark';
});
```

#### 2.3 系统主题变化监听
```typescript
const handleSystemThemeChange = (e: MediaQueryListEvent) => {
  const storedTheme = getStoredTheme();
  if (!storedTheme) {
    // 检查系统偏好，如果系统明确偏好浅色则使用浅色，否则使用深色
    const systemPrefersLight = window.matchMedia('(prefers-color-scheme: light)').matches;
    setThemeMode(systemPrefersLight && !e.matches ? 'light' : 'dark');
  }
};
```

### 3. 全局CSS样式 (`global.css`)

#### 3.1 默认CSS变量（改为深色主题）
```css
/* CSS变量定义 - 默认深色主题 */
:root {
  /* 基础颜色 */
  --color-primary: #0a84ff;
  --color-secondary: #5e5ce6;
  /* ... 其他深色主题变量 */
  
  /* 背景颜色 */
  --bg-primary: #000000;
  --bg-secondary: #1c1c1e;
  --bg-tertiary: #2c2c2e;
  /* ... */
  
  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #ebebf5;
  /* ... */
}
```

#### 3.2 浅色主题变量（移到选择器下）
```css
/* 浅色主题变量 */
[data-theme="light"] {
  /* 基础颜色 */
  --color-primary: #007aff;
  --color-secondary: #5856d6;
  /* ... 浅色主题变量 */
}
```

## 主题优先级逻辑

修改后的主题选择优先级：

1. **本地存储** - 如果用户之前手动设置过主题，使用保存的主题
2. **系统偏好** - 如果系统明确偏好浅色模式，使用浅色主题
3. **默认主题** - 其他情况下使用深色主题

## 测试验证

### 测试场景

1. **首次访问（无本地存储）**
   - ✅ 默认显示深色主题
   - ✅ 系统偏好浅色时显示浅色主题
   - ✅ 系统偏好深色时显示深色主题
   - ✅ 无系统偏好时显示深色主题

2. **主题切换功能**
   - ✅ 手动切换主题正常工作
   - ✅ 主题设置正确保存到本地存储
   - ✅ 刷新页面后保持用户选择的主题

3. **系统主题变化**
   - ✅ 在无用户设置时跟随系统主题变化
   - ✅ 有用户设置时不受系统主题影响

### 验证步骤

1. **清除本地存储测试**
   ```javascript
   // 在浏览器控制台执行
   localStorage.removeItem('research-group-theme');
   location.reload();
   ```
   预期：页面显示深色主题

2. **系统偏好测试**
   - 在操作系统中设置浅色模式
   - 清除本地存储并刷新页面
   - 预期：显示浅色主题

3. **主题切换测试**
   - 点击主题切换按钮
   - 预期：主题正确切换并保存

4. **页面兼容性测试**
   - 访问所有页面（首页、团队、论文等）
   - 预期：所有页面在深色模式下正确显示

## 影响范围

- ✅ 首页 (Home)
- ✅ 团队成员页面 (Team)
- ✅ 研究方向页面 (Research)
- ✅ 项目页面 (Projects)
- ✅ 论文发表页面 (Publications)
- ✅ 新闻页面 (News)
- ✅ 联系我们页面 (Contact)

## 注意事项

1. **向后兼容性**：现有用户的主题偏好设置不受影响
2. **可访问性**：深色主题符合现代Web设计趋势，减少眼部疲劳
3. **性能影响**：修改不影响应用性能，CSS变量切换依然高效
4. **浏览器兼容性**：所有现代浏览器都支持CSS变量和媒体查询

## 回滚方案

如需回滚到浅色默认主题，只需：

1. 修改 `themeConfig.ts`：`export const defaultTheme = lightTheme;`
2. 修改 `ThemeProvider.tsx` 中的 `getSystemTheme()` 返回值为 `'light'`
3. 修改 `global.css` 中的 `:root` 变量为浅色主题值
4. 将浅色主题变量移回 `:root`，深色主题变量移到 `[data-theme="dark"]`
